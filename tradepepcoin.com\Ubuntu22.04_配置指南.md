# Ubuntu 22.04 LTS - 加密货币交易平台配置指南

## 🎯 Ubuntu 22.04 LTS 环境优势

### 系统特性
- **长期支持**: 支持到2027年4月
- **稳定性**: 企业级稳定性，适合生产环境
- **性能**: 优化的内核性能，支持最新硬件
- **安全性**: 定期安全更新，内置安全特性
- **兼容性**: 与宝塔面板完美兼容

## 📋 推荐软件版本配置

### 1. 宝塔面板版本
```
推荐：宝塔面板 7.9.0+
安装命令：
wget -O install.sh https://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

### 2. Web服务器
```
推荐：Nginx 1.18.0+
原因：
- Ubuntu 22.04 默认支持
- 性能优异，内存占用低
- 适合高并发交易场景
```

### 3. PHP版本
```
推荐：PHP 7.4
备选：PHP 8.0
安装命令：
sudo apt update
sudo apt install php7.4-fpm php7.4-mysql php7.4-curl php7.4-mbstring php7.4-xml php7.4-gd php7.4-zip php7.4-opcache
```

### 4. 数据库
```
推荐：MySQL 8.0
备选：MariaDB 10.6
原因：
- Ubuntu 22.04 默认支持 MySQL 8.0
- 性能提升显著
- 安全性增强
```

### 5. 缓存系统
```
推荐：Redis 6.0+
原因：
- 提升交易平台性能
- 支持会话存储
- 减少数据库压力
```

## 🚀 Ubuntu 22.04 安装步骤

### 第一步：系统更新
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install curl wget git unzip -y
```

### 第二步：安装宝塔面板
```bash
wget -O install.sh https://download.bt.cn/install/install-ubuntu_6.0.sh
sudo bash install.sh
```

### 第三步：宝塔面板配置
1. **登录宝塔面板**
   - 访问：`http://服务器IP:8888`
   - 使用安装时显示的账号密码

2. **安装LNMP环境**
   - 软件商店 → 一键安装
   - 选择：Nginx 1.18 + PHP 7.4 + MySQL 8.0 + Redis 6.0

3. **PHP扩展安装**
   ```bash
   # 在宝塔面板中安装以下扩展
   pdo
   pdo_mysql
   mbstring
   curl
   openssl
   zip
   gd
   fileinfo
   opcache
   redis
   ```

## ⚙️ Ubuntu 22.04 系统优化

### 1. 系统参数优化
```bash
# 编辑系统参数
sudo nano /etc/sysctl.conf

# 添加以下配置
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_tw_recycle = 1
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# 应用配置
sudo sysctl -p
```

### 2. PHP-FPM优化
```bash
# 编辑PHP-FPM配置
sudo nano /etc/php/7.4/fpm/pool.d/www.conf

# 优化参数
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000
```

### 3. Nginx优化
```bash
# 编辑Nginx配置
sudo nano /etc/nginx/nginx.conf

# 优化参数
worker_processes auto;
worker_connections 65535;
keepalive_timeout 65;
client_max_body_size 10m;
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

### 4. MySQL优化
```sql
-- 编辑MySQL配置
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

-- 优化参数
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
max_connections = 1000
query_cache_size = 128M
query_cache_type = 1
```

## 🔒 Ubuntu 22.04 安全配置

### 1. 防火墙配置
```bash
# 安装UFW
sudo apt install ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8888/tcp  # 宝塔面板端口
sudo ufw enable
```

### 2. 系统安全更新
```bash
# 设置自动安全更新
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 3. SSH安全配置
```bash
# 编辑SSH配置
sudo nano /etc/ssh/sshd_config

# 安全设置
Port 2222  # 修改默认端口
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2
```

## 📊 性能监控配置

### 1. 安装监控工具
```bash
# 安装htop和iotop
sudo apt install htop iotop nethogs

# 安装系统监控
sudo apt install sysstat
```

### 2. 日志轮转配置
```bash
# 配置logrotate
sudo nano /etc/logrotate.d/nginx
sudo nano /etc/logrotate.d/php7.4-fpm
sudo nano /etc/logrotate.d/mysql
```

## 🔧 常见问题解决

### 问题1：PHP扩展安装失败
```bash
# 解决方案
sudo apt update
sudo apt install php7.4-dev
sudo pecl install redis
```

### 问题2：MySQL连接数限制
```bash
# 检查连接数
mysql -u root -p -e "SHOW VARIABLES LIKE 'max_connections';"

# 修改连接数
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
max_connections = 1000
```

### 问题3：磁盘空间不足
```bash
# 清理系统
sudo apt autoremove
sudo apt autoclean
sudo journalctl --vacuum-time=7d
```

## 📈 性能基准测试

### 1. 系统性能测试
```bash
# CPU测试
sysbench cpu --cpu-max-prime=20000 run

# 内存测试
sysbench memory --memory-block-size=1K --memory-total-size=100G run

# 磁盘测试
sysbench fileio --file-test-mode=seqwr run
```

### 2. Web服务器测试
```bash
# 安装ab测试工具
sudo apt install apache2-utils

# 压力测试
ab -n 1000 -c 100 http://yourdomain.com/
```

## 🎯 推荐配置总结

| 组件 | 推荐版本 | 原因 |
|------|----------|------|
| 操作系统 | Ubuntu 22.04 LTS | 长期支持，稳定性好 |
| Web服务器 | Nginx 1.18+ | 性能优异，适合高并发 |
| PHP版本 | PHP 7.4 | 与ThinkPHP兼容性最好 |
| 数据库 | MySQL 8.0 | 性能提升，安全性增强 |
| 缓存 | Redis 6.0+ | 提升应用性能 |
| 监控 | htop + sysstat | 系统性能监控 |

## 📞 技术支持

如果遇到问题：
1. 查看系统日志：`sudo journalctl -f`
2. 查看Nginx日志：`sudo tail -f /var/log/nginx/error.log`
3. 查看PHP日志：`sudo tail -f /var/log/php7.4-fpm.log`
4. 查看MySQL日志：`sudo tail -f /var/log/mysql/error.log` 