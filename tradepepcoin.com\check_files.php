<?php
echo "<h2>文件系统检查</h2>";

echo "<h3>根目录内容：</h3>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file == '.' || $file == '..') continue;
    
    if (is_dir($file)) {
        echo "📁 $file/<br>";
        
        // 如果是ThinkPHP目录，显示其内容
        if ($file == 'ThinkPHP') {
            $tp_files = scandir($file);
            foreach ($tp_files as $tp_file) {
                if ($tp_file != '.' && $tp_file != '..') {
                    echo "&nbsp;&nbsp;&nbsp;&nbsp;- $tp_file<br>";
                }
            }
        }
    } else {
        echo "📄 $file<br>";
    }
}

echo "<h3>关键文件检查：</h3>";
$key_files = [
    'index.php',
    '.htaccess',
    'ThinkPHP/ThinkPHP.php',
    'Application/Common/Conf/config.php'
];

foreach ($key_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file (" . filesize($file) . " bytes)<br>";
    } else {
        echo "❌ $file<br>";
    }
}

echo "<h3>目录权限：</h3>";
$dirs = ['Runtime', 'Upload', 'Application'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        echo "$dir: 可读=" . (is_readable($dir) ? '✅' : '❌') . 
             " 可写=" . (is_writable($dir) ? '✅' : '❌') . "<br>";
    } else {
        echo "❌ $dir 目录不存在<br>";
    }
}
?>