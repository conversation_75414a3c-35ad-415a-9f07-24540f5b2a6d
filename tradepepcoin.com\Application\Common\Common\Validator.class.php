<?php
class Validator {
    
    public static function validateTradeAmount($amount, $min = 0.0001, $max = 1000) {
        if (!is_numeric($amount) || $amount < $min || $amount > $max) {
            return false;
        }
        return true;
    }
    
    public static function validateCoinSymbol($symbol) {
        $allowed_symbols = array('BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'LTC', 'XRP');
        return in_array(strtoupper($symbol), $allowed_symbols);
    }
    
    public static function sanitizeInput($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}
?>