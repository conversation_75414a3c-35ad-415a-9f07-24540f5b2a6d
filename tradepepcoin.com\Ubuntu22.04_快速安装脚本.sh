#!/bin/bash

# Ubuntu 22.04 LTS 加密货币交易平台快速安装脚本
# 作者：AI助手
# 版本：1.0
# 日期：2024年

echo "=========================================="
echo "Ubuntu 22.04 LTS 加密货币交易平台安装脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用sudo运行此脚本"
    exit 1
fi

# 检查Ubuntu版本
UBUNTU_VERSION=$(lsb_release -rs)
if [ "$UBUNTU_VERSION" != "22.04" ]; then
    echo "⚠️  警告：此脚本专为Ubuntu 22.04设计，当前版本：$UBUNTU_VERSION"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "✅ 系统检查通过"

# 第一步：系统更新
echo "📦 正在更新系统..."
apt update && apt upgrade -y
apt install -y curl wget git unzip htop iotop nethogs sysstat

# 第二步：安装宝塔面板
echo "🔧 正在安装宝塔面板..."
wget -O install.sh https://download.bt.cn/install/install-ubuntu_6.0.sh
bash install.sh

# 第三步：系统优化
echo "⚙️  正在优化系统参数..."

# 备份原配置文件
cp /etc/sysctl.conf /etc/sysctl.conf.backup

# 添加系统优化参数
cat >> /etc/sysctl.conf << EOF

# 网络优化参数
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_tw_recycle = 1

# 内存优化参数
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF

# 应用系统参数
sysctl -p

# 第四步：防火墙配置
echo "🔒 正在配置防火墙..."
apt install -y ufw

# 配置防火墙规则
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8888/tcp
ufw --force enable

# 第五步：安全更新配置
echo "🛡️  正在配置自动安全更新..."
apt install -y unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades

# 第六步：创建安装完成提示
cat > /root/安装完成提示.txt << EOF
==========================================
Ubuntu 22.04 LTS 安装完成！
==========================================

🎉 系统基础环境已安装完成

📋 接下来需要手动完成的步骤：

1. 访问宝塔面板：
   http://$(curl -s ifconfig.me):8888
   
2. 在宝塔面板中安装以下软件：
   - Nginx 1.18+
   - PHP 7.4
   - MySQL 8.0
   - Redis 6.0+

3. 安装PHP扩展：
   - pdo
   - pdo_mysql
   - mbstring
   - curl
   - openssl
   - zip
   - gd
   - fileinfo
   - opcache
   - redis

4. 上传网站源码并按照教程配置

📞 技术支持：
- 系统日志：sudo journalctl -f
- 宝塔面板日志：/www/server/panel/logs/
- 网站日志：/www/wwwlogs/

🔒 安全提醒：
- 立即修改宝塔面板默认密码
- 配置SSL证书
- 定期备份数据
- 监控系统资源使用情况

==========================================
EOF

echo "✅ 安装完成！"
echo "📋 请查看 /root/安装完成提示.txt 获取后续步骤"
echo "🌐 宝塔面板地址：http://$(curl -s ifconfig.me):8888"

# 显示系统信息
echo ""
echo "📊 系统信息："
echo "操作系统：$(lsb_release -d | cut -f2)"
echo "内核版本：$(uname -r)"
echo "CPU核心：$(nproc)"
echo "内存大小：$(free -h | awk 'NR==2{print $2}')"
echo "磁盘空间：$(df -h / | awk 'NR==2{print $4}') 可用"

echo ""
echo "🎯 推荐配置："
echo "Web服务器：Nginx 1.18+"
echo "PHP版本：PHP 7.4"
echo "数据库：MySQL 8.0"
echo "缓存：Redis 6.0+"

echo ""
echo "✨ 安装脚本执行完成！" 