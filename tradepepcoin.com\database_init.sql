-- 加密货币交易平台数据库初始化
-- 创建时间: 2024-01-01
-- 版本: 1.0

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 用户表
-- ----------------------------
DROP TABLE IF EXISTS `tw_users`;
CREATE TABLE `tw_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `salt` varchar(32) NOT NULL COMMENT '密码盐',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `level` tinyint(1) DEFAULT '0' COMMENT 'VIP等级',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1正常 0禁用',
  `last_login_ip` varchar(15) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` int(11) DEFAULT NULL COMMENT '最后登录时间',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ----------------------------
-- 用户资产表
-- ----------------------------
DROP TABLE IF EXISTS `tw_user_assets`;
CREATE TABLE `tw_user_assets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin` varchar(10) NOT NULL COMMENT '币种',
  `balance` decimal(20,8) DEFAULT '0.00000000' COMMENT '可用余额',
  `frozen` decimal(20,8) DEFAULT '0.00000000' COMMENT '冻结余额',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_coin` (`user_id`,`coin`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户资产表';

-- ----------------------------
-- 交易对表
-- ----------------------------
DROP TABLE IF EXISTS `tw_trading_pairs`;
CREATE TABLE `tw_trading_pairs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `symbol` varchar(20) NOT NULL COMMENT '交易对符号',
  `base_coin` varchar(10) NOT NULL COMMENT '基础币种',
  `quote_coin` varchar(10) NOT NULL COMMENT '计价币种',
  `price` decimal(20,8) DEFAULT '0.00000000' COMMENT '当前价格',
  `change_24h` decimal(10,4) DEFAULT '0.0000' COMMENT '24小时涨跌幅',
  `volume_24h` decimal(20,8) DEFAULT '0.00000000' COMMENT '24小时成交量',
  `high_24h` decimal(20,8) DEFAULT '0.00000000' COMMENT '24小时最高价',
  `low_24h` decimal(20,8) DEFAULT '0.00000000' COMMENT '24小时最低价',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1启用 0禁用',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `symbol` (`symbol`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易对表';

-- ----------------------------
-- 订单表
-- ----------------------------
DROP TABLE IF EXISTS `tw_orders`;
CREATE TABLE `tw_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` varchar(32) NOT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `type` enum('buy','sell') NOT NULL COMMENT '订单类型',
  `price` decimal(20,8) NOT NULL COMMENT '价格',
  `amount` decimal(20,8) NOT NULL COMMENT '数量',
  `filled` decimal(20,8) DEFAULT '0.00000000' COMMENT '已成交数量',
  `fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '手续费',
  `status` enum('pending','filled','cancelled','partial') DEFAULT 'pending' COMMENT '订单状态',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`),
  KEY `user_id` (`user_id`),
  KEY `symbol` (`symbol`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- ----------------------------
-- 交易记录表
-- ----------------------------
DROP TABLE IF EXISTS `tw_trades`;
CREATE TABLE `tw_trades` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trade_id` varchar(32) NOT NULL COMMENT '交易ID',
  `buy_order_id` varchar(32) NOT NULL COMMENT '买单ID',
  `sell_order_id` varchar(32) NOT NULL COMMENT '卖单ID',
  `buy_user_id` int(11) NOT NULL COMMENT '买方用户ID',
  `sell_user_id` int(11) NOT NULL COMMENT '卖方用户ID',
  `symbol` varchar(20) NOT NULL COMMENT '交易对',
  `price` decimal(20,8) NOT NULL COMMENT '成交价格',
  `amount` decimal(20,8) NOT NULL COMMENT '成交数量',
  `buy_fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '买方手续费',
  `sell_fee` decimal(20,8) DEFAULT '0.00000000' COMMENT '卖方手续费',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `trade_id` (`trade_id`),
  KEY `symbol` (`symbol`),
  KEY `buy_user_id` (`buy_user_id`),
  KEY `sell_user_id` (`sell_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易记录表';

-- ----------------------------
-- 充值记录表
-- ----------------------------
DROP TABLE IF EXISTS `tw_deposits`;
CREATE TABLE `tw_deposits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin` varchar(10) NOT NULL COMMENT '币种',
  `amount` decimal(20,8) NOT NULL COMMENT '充值金额',
  `txid` varchar(100) DEFAULT NULL COMMENT '交易哈希',
  `address` varchar(100) DEFAULT NULL COMMENT '充值地址',
  `confirmations` int(11) DEFAULT '0' COMMENT '确认数',
  `status` enum('pending','confirmed','failed') DEFAULT 'pending' COMMENT '状态',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `txid` (`txid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';

-- ----------------------------
-- 提现记录表
-- ----------------------------
DROP TABLE IF EXISTS `tw_withdrawals`;
CREATE TABLE `tw_withdrawals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coin` varchar(10) NOT NULL COMMENT '币种',
  `amount` decimal(20,8) NOT NULL COMMENT '提现金额',
  `fee` decimal(20,8) NOT NULL COMMENT '手续费',
  `actual_amount` decimal(20,8) NOT NULL COMMENT '实际到账金额',
  `address` varchar(100) NOT NULL COMMENT '提现地址',
  `txid` varchar(100) DEFAULT NULL COMMENT '交易哈希',
  `status` enum('pending','processing','completed','failed','cancelled') DEFAULT 'pending' COMMENT '状态',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现记录表';

-- ----------------------------
-- 系统配置表
-- ----------------------------
DROP TABLE IF EXISTS `tw_config`;
CREATE TABLE `tw_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '配置名称',
  `value` text COMMENT '配置值',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_time` int(11) NOT NULL COMMENT '创建时间',
  `update_time` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ----------------------------
-- 插入初始数据
-- ----------------------------

-- 插入交易对
INSERT INTO `tw_trading_pairs` VALUES 
(1, 'BTCUSDT', 'BTC', 'USDT', 45000.00000000, 2.5000, 1234.56789000, 46000.00000000, 44000.00000000, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'ETHUSDT', 'ETH', 'USDT', 3200.00000000, 1.8000, 5678.90123000, 3300.00000000, 3100.00000000, 1, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'BNBUSDT', 'BNB', 'USDT', 420.00000000, -0.5000, 2345.67890000, 430.00000000, 410.00000000, 1, 3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 插入系统配置
INSERT INTO `tw_config` VALUES 
(1, 'site_name', '加密货币交易平台', '网站名称', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'trade_fee_rate', '0.002', '交易手续费率', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'withdraw_min_btc', '0.001', 'BTC最小提现金额', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, 'withdraw_fee_btc', '0.0005', 'BTC提现手续费', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

SET FOREIGN_KEY_CHECKS = 1;