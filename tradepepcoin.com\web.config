<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- URL重写规则 -->
        <rewrite>
            <rules>
                <rule name="ThinkPHP" stopProcessing="true">
                    <match url="^(.*)$" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php?s=/{R:1}" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- 安全设置 -->
        <security>
            <requestFiltering>
                <!-- 隐藏敏感文件 -->
                <hiddenSegments>
                    <add segment="Runtime" />
                    <add segment="Application" />
                </hiddenSegments>
                <fileExtensions>
                    <add fileExtension=".env" allowed="false" />
                    <add fileExtension=".sql" allowed="false" />
                </fileExtensions>
            </requestFiltering>
        </security>
        
        <!-- HTTP响应头 -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-Frame-Options" value="DENY" />
                <add name="X-XSS-Protection" value="1; mode=block" />
            </customHeaders>
        </httpProtocol>
    </system.webServer>
</configuration>