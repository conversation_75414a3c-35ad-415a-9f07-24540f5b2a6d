<?php
// ThinkPHP框架安装脚本
echo "<h2>ThinkPHP框架安装</h2>";

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.0.0', '<')) {
    die("❌ PHP版本过低，需要PHP 7.0+");
}
echo "✅ PHP版本: " . PHP_VERSION . "<br>";

// 检查必要扩展
$required_extensions = ['curl', 'zip'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ {$ext} 扩展已安装<br>";
    } else {
        echo "❌ {$ext} 扩展未安装<br>";
        die("请在宝塔面板安装 {$ext} 扩展");
    }
}

// 下载ThinkPHP 3.2.3
echo "<h3>下载ThinkPHP框架...</h3>";

if (!file_exists('ThinkPHP')) {
    echo "正在下载ThinkPHP框架...<br>";
    
    // 多个下载源
    $download_urls = [
        "https://github.com/top-think/thinkphp/archive/3.2.3.zip",
        "https://codeload.github.com/top-think/thinkphp/zip/3.2.3"
    ];
    
    $zip_file = "thinkphp.zip";
    $downloaded = false;
    
    foreach ($download_urls as $url) {
        echo "尝试从: $url<br>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        
        $data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($data && $http_code == 200) {
            file_put_contents($zip_file, $data);
            echo "✅ 下载成功 (" . round(strlen($data)/1024/1024, 2) . "MB)<br>";
            $downloaded = true;
            break;
        } else {
            echo "❌ 下载失败 (HTTP: $http_code)<br>";
        }
    }
    
    if (!$downloaded) {
        echo "<br>❌ 所有下载源都失败，请手动安装：<br>";
        echo "1. 下载：https://github.com/top-think/thinkphp/archive/3.2.3.zip<br>";
        echo "2. 解压后重命名为 ThinkPHP<br>";
        echo "3. 上传到网站根目录<br>";
        exit;
    }
    
    // 解压文件
    if (class_exists('ZipArchive')) {
        $zip = new ZipArchive;
        if ($zip->open($zip_file) === TRUE) {
            $zip->extractTo('./');
            $zip->close();
            
            // 重命名目录
            if (file_exists('thinkphp-3.2.3')) {
                rename('thinkphp-3.2.3', 'ThinkPHP');
                echo "✅ ThinkPHP框架安装成功<br>";
            } else {
                echo "❌ 解压后目录结构异常<br>";
            }
        } else {
            echo "❌ 解压失败<br>";
        }
    } else {
        echo "❌ ZipArchive扩展未安装<br>";
    }
    
    // 删除zip文件
    if (file_exists($zip_file)) {
        unlink($zip_file);
    }
    
} else {
    echo "✅ ThinkPHP框架已存在<br>";
}

// 检查安装结果
if (file_exists('ThinkPHP/ThinkPHP.php')) {
    echo "<br>✅ 安装验证成功！<br>";
    echo "<a href='/' style='background:#007cba;color:white;padding:10px 20px;text-decoration:none;border-radius:5px;'>访问网站首页</a><br><br>";
    echo "<strong>⚠️ 安装完成后请删除此文件！</strong>";
} else {
    echo "<br>❌ 安装验证失败，ThinkPHP.php文件不存在<br>";
}
?>