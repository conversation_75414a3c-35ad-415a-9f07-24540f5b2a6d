<?php
// 加载环境变量
require_once 'load_env.php';
loadEnv();

// 检查PHP版本
if(version_compare(PHP_VERSION,'5.3.0','<'))  die('require PHP > 5.3.0 !');

// 定义应用路径
define('APP_PATH','./Application/');
define('RUNTIME_PATH','./Runtime/');

// 检查用户代理
function wherecome() {
    if (!isset($_SERVER['HTTP_USER_AGENT'])) return false;
    $mobile_agents = array('iphone','android','phone','mobile','wap','netfront','java','opera mobi','opera mini');
    $agent = strtolower($_SERVER['HTTP_USER_AGENT']);
    foreach($mobile_agents as $device) {
        if(strpos($agent, $device) !== false) {
            return true;
        }
    }
    return false;
}

if(wherecome()) {
    define('WHERECOME','Mobile');
} else {
    define('WHERECOME','Home');
}

// 启动ThinkPHP
require './ThinkPHP/ThinkPHP/ThinkPHP.php';
?>
