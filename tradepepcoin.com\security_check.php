<?php
// 安全检查文件 - 部署完成后删除
echo "<h2>安全配置检查</h2>";

// 检查敏感文件是否被保护
$sensitive_files = array('.env', '数据库初始化.sql', 'test_env.php');
$protected_dirs = array('Runtime', 'Application');

echo "<h3>敏感文件保护检查：</h3>";
foreach($sensitive_files as $file) {
    if(file_exists($file)) {
        echo "✅ {$file} 文件存在<br>";
    } else {
        echo "❌ {$file} 文件不存在<br>";
    }
}

echo "<h3>目录保护检查：</h3>";
foreach($protected_dirs as $dir) {
    if(file_exists($dir . '/index.html')) {
        echo "✅ {$dir}/index.html 保护文件存在<br>";
    } else {
        echo "❌ {$dir}/index.html 保护文件缺失<br>";
    }
}

echo "<h3>安全文件检查：</h3>";
if(file_exists('.htaccess')) {
    echo "✅ .htaccess 文件存在<br>";
} else {
    echo "❌ .htaccess 文件不存在<br>";
}

if(file_exists('web.config')) {
    echo "✅ web.config 文件存在<br>";
} else {
    echo "⚠️ web.config 文件不存在（IIS服务器需要）<br>";
}

echo "<br><strong>注意：部署完成后请删除此检查文件！</strong>";
?>