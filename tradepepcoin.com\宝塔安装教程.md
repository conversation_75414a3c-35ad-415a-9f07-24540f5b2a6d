# 加密货币交易平台 - 宝塔面板安装教程

## 📋 安装前准备

### 1. 宝塔面板环境要求
- **操作系统**: CentOS 7+ / Ubuntu 18+ / Debian 9+
- **PHP版本**: 7.4 或 8.0
- **MySQL版本**: 5.7+ 或 MariaDB 10.3+
- **Nginx版本**: 1.18+ 或 Apache 2.4+

### 2. 必需PHP扩展
- pdo
- pdo_mysql
- mbstring
- curl
- openssl
- zip
- gd
- fileinfo

## 🚀 详细安装步骤

### 第一步：创建网站

1. **登录宝塔面板**
2. **添加站点**
   - 点击左侧菜单"网站"
   - 点击"添加站点"
   - 填写域名：`yourdomain.com`
   - 选择PHP版本：**PHP 7.4**
   - 创建数据库：勾选"创建数据库"
   - 数据库名：`tradeppapcoin_co`
   - 用户名：`tradeppapcoin_co`
   - 密码：设置强密码

### 第二步：上传源代码

1. **进入网站根目录**
   - 点击网站列表中的"根目录"
   - 删除默认的index.html文件

2. **上传所有源代码文件**
   - 上传以下文件到网站根目录：
     - `index.php`
     - `.htaccess`
     - `web.config`
     - `security_check.php`
     - `install_thinkphp.php`
     - `database_init.sql`
     - `env.example`
     - `伪静态规则.conf`
   - 上传以下目录：
     - `Application/`
     - `Runtime/`
     - `Upload/`

### 第三步：安装ThinkPHP框架

1. **访问安装脚本**
   - 浏览器访问：`http://yourdomain.com/install_thinkphp.php`
   - 等待自动下载和安装ThinkPHP框架
   - 看到"✅ ThinkPHP框架安装成功"后继续

2. **检查安装结果**
   - 确认网站根目录下出现`ThinkPHP/`文件夹
   - 删除`install_thinkphp.php`文件

### 第四步：配置数据库

1. **导入数据库结构**
   - 进入宝塔面板 → 数据库
   - 点击刚创建的数据库
   - 点击"导入"
   - 选择`database_init.sql`文件
   - 点击"导入"

2. **配置环境变量**
   - 复制`env.example`为`.env`
   - 编辑`.env`文件，修改数据库配置：
   ```bash
   DB_HOST=127.0.0.1
   DB_NAME=tradeppapcoin_co
   DB_USER=tradeppapcoin_co
   DB_PWD=你的数据库密码
   DB_PORT=3306
   ```

### 第五步：配置伪静态

#### Nginx配置（推荐）
1. **进入网站设置**
   - 点击网站列表中的"设置"
   - 点击"伪静态"

2. **添加伪静态规则**
   - 选择"自定义"
   - 粘贴以下规则：
   ```nginx
   location / {
       if (!-e $request_filename) {
           rewrite ^(.*)$ /index.php?s=/$1 last;
           break;
       }
   }
   ```

#### Apache配置
- 确保`.htaccess`文件已上传
- 在网站设置中启用"伪静态"

### 第六步：设置文件权限

1. **设置目录权限**
   ```bash
   chmod 755 Runtime/
   chmod 755 Upload/
   chmod 755 Application/
   ```

2. **设置文件权限**
   ```bash
   chmod 644 index.php
   chmod 644 .htaccess
   chmod 644 .env
   ```

### 第七步：安全检查

1. **访问安全检查页面**
   - 浏览器访问：`http://yourdomain.com/security_check.php`
   - 确认所有检查项都显示"✅"

2. **删除检查文件**
   - 删除`security_check.php`
   - 删除`database_init.sql`
   - 删除`env.example`

### 第八步：测试访问

1. **访问网站首页**
   - 浏览器访问：`http://yourdomain.com`
   - 应该看到网站首页

2. **测试管理员登录**
   - 访问：`http://yourdomain.com/admin`
   - 用户名：`admin`
   - 密码：`admin123`

## 🔧 常见问题解决

### 问题1：ThinkPHP框架下载失败
**解决方案**：
- 手动下载ThinkPHP 3.2.3
- 解压后重命名为`ThinkPHP`
- 上传到网站根目录

### 问题2：数据库连接失败
**解决方案**：
- 检查`.env`文件中的数据库配置
- 确认数据库用户权限
- 检查防火墙设置

### 问题3：伪静态不生效
**解决方案**：
- 确认Nginx/Apache配置正确
- 重启Web服务
- 检查`.htaccess`文件权限

### 问题4：文件权限错误
**解决方案**：
- 设置Runtime目录可写权限
- 设置Upload目录可写权限
- 检查PHP运行用户权限

## 📞 技术支持

如果遇到问题，请检查：
1. 宝塔面板错误日志
2. PHP错误日志
3. Nginx/Apache错误日志
4. 网站访问日志

## 🔒 安全建议

1. **修改默认密码**
   - 立即修改管理员密码
   - 修改数据库密码

2. **启用HTTPS**
   - 在宝塔面板中申请SSL证书
   - 强制HTTPS访问

3. **定期备份**
   - 设置数据库自动备份
   - 设置网站文件备份

4. **安全防护**
   - 启用宝塔安全防护
   - 设置防火墙规则 