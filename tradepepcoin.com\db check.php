<?php
echo "<h2>数据库配置检查</h2>";

// 读取.env文件
$env_data = array();
if (file_exists('.env')) {
    $lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $env_data[trim($name)] = trim($value, '"\'');
        }
    }
    echo "✅ .env 文件已读取<br><br>";
} else {
    echo "❌ .env 文件不存在<br><br>";
}

echo "<h3>当前配置：</h3>";
$db_host = isset($env_data['DB_HOST']) ? $env_data['DB_HOST'] : '127.0.0.1';
$db_name = isset($env_data['DB_NAME']) ? $env_data['DB_NAME'] : 'tradeppapcoin_co';
$db_user = isset($env_data['DB_USER']) ? $env_data['DB_USER'] : 'tradeppapcoin_co';
$db_pwd = isset($env_data['DB_PWD']) ? $env_data['DB_PWD'] : '';

echo "DB_HOST: $db_host<br>";
echo "DB_NAME: $db_name<br>";
echo "DB_USER: $db_user<br>";
echo "DB_PWD: " . ($db_pwd ? '已设置' : '❌ 未设置') . "<br><br>";

echo "<h3>测试数据库连接：</h3>";

if (empty($db_pwd)) {
    echo "❌ 数据库密码未设置，无法连接<br>";
    echo "<br><strong>请在宝塔面板查看数据库信息并更新.env文件</strong><br>";
} else {
    try {
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pwd);
        echo "✅ 数据库连接成功！<br>";
        
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "📋 表数量：" . count($tables) . "<br>";
        
        if (count($tables) > 0) {
            echo "📋 部分表名：" . implode(', ', array_slice($tables, 0, 5)) . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ 数据库连接失败：" . $e->getMessage() . "<br>";
    }
}

echo "<br><h3>系统信息：</h3>";
echo "PHP版本：" . PHP_VERSION . "<br>";
echo "当前时间：" . date('Y-m-d H:i:s') . "<br>";
?>