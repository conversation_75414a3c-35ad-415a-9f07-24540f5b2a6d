# 隐藏敏感文件
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "test_env.php">
    Order allow,deny
    Deny from all
</Files>

# 防止访问敏感目录
RedirectMatch 403 ^/Runtime/.*$
RedirectMatch 403 ^/Application/.*$

# 防止访问配置文件
<FilesMatch "\.(conf|config|ini)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# URL重写规则（ThinkPHP伪静态）
<IfModule mod_rewrite.c>
    RewriteEngine on
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^(.*)$ index.php?s=/$1 [QSA,PT,L]
</IfModule>

# 防止目录浏览
Options -Indexes

# 安全头设置
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>