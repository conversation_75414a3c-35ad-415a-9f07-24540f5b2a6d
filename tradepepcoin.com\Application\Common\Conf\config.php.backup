<?php
return array(
    //'配置项'=>'配置值'
    
    // 数据库设置 - 建议使用环境变量
    'DB_TYPE'               =>  'mysql',     // 数据库类型
    'DB_HOST'               =>  getenv('DB_HOST') ?: '127.0.0.1', // 服务器地址
    'DB_NAME'               =>  getenv('DB_NAME') ?: 'tradeppapcoin_co', // 数据库名
    'DB_USER'               =>  getenv('DB_USER') ?: 'tradeppapcoin_co', // 用户名
    'DB_PWD'                =>  getenv('DB_PWD') ?: '', // 密码 - 移除硬编码
    'DB_PORT'               =>  getenv('DB_PORT') ?: '3306', // 端口
    'DB_PREFIX'             =>  'tw_',    // 数据库表前缀
    'DB_PARAMS'          	=>  array(), // 数据库连接参数    
    'DB_DEBUG'  			=>  false, // 生产环境关闭调试
    'DB_FIELDS_CACHE'       =>  true,        // 启用字段缓存
    'DB_CHARSET'            =>  'utf8mb4',      // 使用utf8mb4支持emoji
    
    // 默认设定
    'DEFAULT_MODULE'        =>  'Home',  // 默认模块
    'DEFAULT_CONTROLLER'    =>  'Index', // 默认控制器名称
    'DEFAULT_ACTION'        =>  'index', // 默认操作名称
    'DEFAULT_CHARSET'       =>  'utf-8', // 默认输出编码
    'DEFAULT_TIMEZONE'      =>  'PRC',	// 默认时区
    
    // 系统设置
    'SITE_NAME'             =>  '加密货币交易平台',
    'SITE_URL'              =>  getenv('SITE_URL') ?: 'http://localhost',
    'SITE_EMAIL'            =>  '<EMAIL>',
    'SITE_KEYWORDS'         =>  '加密货币,数字货币,比特币,以太坊,交易平台',
    'SITE_DESCRIPTION'      =>  '专业的加密货币交易平台',
    
    // URL设置
    'URL_CASE_INSENSITIVE'  =>  true,   // URL不区分大小写
    'URL_MODEL'             =>  2,       // REWRITE模式
    'URL_HTML_SUFFIX'       =>  '',      // URL后缀
    
    // 模板设置
    'TMPL_CONTENT_TYPE'     =>  'text/html', // 默认模板文件类型
    'TMPL_FILE_DEPR'        =>  '/', // 模板文件分割符
    'TMPL_PARSE_STRING'     =>  array(
        '__UPLOAD__' => __ROOT__ . '/Upload',
        '__PUBLIC__' => __ROOT__ . '/Public',
        '__CSS__' => __ROOT__ . '/Public/css',
        '__JS__' => __ROOT__ . '/Public/js',
        '__IMG__' => __ROOT__ . '/Public/images',
    ),
    
    // 日志设置 - 优化日志配置
    'LOG_RECORD'            => true,   // 开启日志记录
    'LOG_TYPE'              => 'File', // 日志记录类型
    'LOG_PATH'              => './Runtime/Logs/', // 明确日志路径
    'LOG_LEVEL'             => 'EMERG,ALERT,CRIT,ERR,WARN,NOTICE,INFO,DEBUG', // 记录所有级别
    'LOG_FILE_SIZE'         => 2097152, // 日志文件大小限制 2MB
    'LOG_EXCEPTION_RECORD'  => true,    // 记录异常信息
    
    // SESSION设置 - 增强安全性
    'SESSION_AUTO_START'    =>  true,    // 自动开启Session
    'SESSION_PATH'          =>  './Runtime/Session/', // Session存储路径
    'SESSION_OPTIONS'       =>  array(
        'name' => 'CRYPTO_TRADE_SESSION',
        'expire' => 7200, // 2小时过期
        'use_trans_sid' => 0,
        'use_only_cookies' => 1,
        'cookie_httponly' => 1,
        'cookie_secure' => false, // HTTPS环境设为true
    ),
    'SESSION_TYPE'          =>  '', 
    'SESSION_PREFIX'        =>  'crypto_', 
    
    // COOKIE设置 - 增强安全性
    'COOKIE_EXPIRE'         =>  7200,    // 2小时有效期
    'COOKIE_DOMAIN'         =>  '',      
    'COOKIE_PATH'           =>  '/',     
    'COOKIE_PREFIX'         =>  'crypto_',      
    'COOKIE_SECURE'         =>  false,   // HTTPS环境设为true
    'COOKIE_HTTPONLY'       =>  true,    // 防止XSS攻击
    
    // 缓存设置 - 性能优化
    'DATA_CACHE_TIME'       => 300,      // 缓存5分钟
    'DATA_CACHE_COMPRESS'   => true,     // 开启压缩
    'DATA_CACHE_CHECK'      => true,     // 缓存检查
    'DATA_CACHE_TYPE'       => 'File',   // 缓存类型
    'DATA_CACHE_PATH'       => './Runtime/Cache/', // 修复缓存路径
    'DATA_CACHE_SUBDIR'     => true,     // 子目录缓存
    
    // 安全设置
    'TOKEN_ON'              => true,     // 开启令牌验证
    'TOKEN_NAME'            => '__hash__',
    'TOKEN_TYPE'            => 'sha256', // 使用更安全的算法
    'TOKEN_RESET'           => true,
    
    // 上传设置
    'UPLOAD_MAX_SIZE'       => 5242880,  // 5MB
    'UPLOAD_ALLOWED_EXTS'   => array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'),
    'UPLOAD_SAVE_PATH'      => './Upload/',
    'UPLOAD_REPLACE'        => false,
    'UPLOAD_AUTO_SUB'       => true,     // 自动子目录
    
    // 交易平台专用配置
    'TRADE_CONFIG' => array(
        // 手续费率配置
        'FEE_RATES' => array(
            0 => 0.002,  // 普通用户 0.2%
            1 => 0.0018, // VIP1 0.18%
            2 => 0.0016, // VIP2 0.16%
            3 => 0.0014, // VIP3 0.14%
            4 => 0.0012, // VIP4 0.12%
            5 => 0.001,  // VIP5 0.1%
        ),
        
        // 提现手续费
        'WITHDRAW_FEES' => array(
            'BTC' => 0.0005,
            'ETH' => 0.01,
            'USDT' => 5,
            'BNB' => 0.01,
            'ADA' => 2,
            'DOT' => 0.1,
        ),
        
        // 最小提现金额
        'MIN_WITHDRAW' => array(
            'BTC' => 0.001,
            'ETH' => 0.01,
            'USDT' => 10,
            'BNB' => 0.01,
            'ADA' => 10,
            'DOT' => 1,
        ),
        
        // 币种精度配置
        'COIN_DECIMALS' => array(
            'BTC' => 8,
            'ETH' => 6,
            'USDT' => 2,
            'BNB' => 6,
            'ADA' => 6,
            'DOT' => 4,
        ),
        
        // 交易限制
        'TRADE_LIMITS' => array(
            'MIN_TRADE_AMOUNT' => 0.0001,
            'MAX_TRADE_AMOUNT' => 1000,
            'DAILY_WITHDRAW_LIMIT' => 100, // BTC等值
        ),
    ),
    
    // API配置
    'API_CONFIG' => array(
        'RATE_LIMIT' => 100,        // 每分钟请求限制
        'TIMEOUT' => 30,            // 超时时间
        'ALLOWED_IPS' => array(),   // IP白名单
        'SECRET_KEY' => getenv('API_SECRET') ?: 'your-secret-key-here',
    ),
    
    // 邮件配置
    'MAIL_CONFIG' => array(
        'SMTP_HOST' => getenv('SMTP_HOST') ?: 'smtp.gmail.com',
        'SMTP_PORT' => getenv('SMTP_PORT') ?: 587,
        'SMTP_USER' => getenv('SMTP_USER') ?: '',
        'SMTP_PASS' => getenv('SMTP_PASS') ?: '',
        'FROM_EMAIL' => getenv('FROM_EMAIL') ?: '<EMAIL>',
        'FROM_NAME' => '加密货币交易平台',
    ),
    
    // Redis配置（如果使用Redis缓存）
    'REDIS_CONFIG' => array(
        'HOST' => getenv('REDIS_HOST') ?: '127.0.0.1',
        'PORT' => getenv('REDIS_PORT') ?: 6379,
        'PASSWORD' => getenv('REDIS_PASSWORD') ?: '',
        'SELECT' => 0,
        'TIMEOUT' => 3,
        'EXPIRE' => 3600,
    ),
    
    // 多语言设置
    'LANG_SWITCH_ON' => true,
    'LANG_AUTO_DETECT' => true,
    'DEFAULT_LANG' => 'zh-cn',
    'LANG_LIST' => 'zh-cn,en-us,ja-jp,ko-kr',
    'VAR_LANGUAGE' => 'lang',
    
    // 错误页面
    'TMPL_ACTION_ERROR' => './Public/error.html',
    'TMPL_ACTION_SUCCESS' => './Public/success.html',
    
    // 加载扩展配置文件
    'LOAD_EXT_CONFIG' => 'user',
);?>
