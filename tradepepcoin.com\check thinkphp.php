<?php
echo "<h2>ThinkPHP目录结构检查</h2>";

function listDirectory($dir, $prefix = '') {
    if (!is_dir($dir)) {
        echo "❌ 目录不存在: $dir<br>";
        return;
    }
    
    $files = scandir($dir);
    foreach ($files as $file) {
        if ($file == '.' || $file == '..') continue;
        
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            echo "📁 {$prefix}$file/<br>";
            if ($prefix == '' && $file == 'ThinkPHP') {
                listDirectory($path, '&nbsp;&nbsp;&nbsp;&nbsp;');
            }
        } else {
            echo "📄 {$prefix}$file<br>";
        }
    }
}

echo "<h3>根目录文件：</h3>";
listDirectory('.');

echo "<h3>关键文件检查：</h3>";
$key_files = [
    'ThinkPHP/ThinkPHP.php',
    'ThinkPHP/Common/functions.php',
    'ThinkPHP/Library/Think/Think.class.php',
    'ThinkPHP/Mode/common.php'
];

foreach ($key_files as $file) {
    if (file_exists($file)) {
        echo "✅ $file<br>";
    } else {
        echo "❌ $file<br>";
    }
}

echo "<h3>ThinkPHP目录内容：</h3>";
if (is_dir('ThinkPHP')) {
    $tp_files = scandir('ThinkPHP');
    foreach ($tp_files as $file) {
        if ($file != '.' && $file != '..') {
            echo "- $file<br>";
        }
    }
} else {
    echo "❌ ThinkPHP目录不存在<br>";
}
?>