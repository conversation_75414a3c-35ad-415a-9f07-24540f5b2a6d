private function getUserAssets($user_id, $type = 'spot') {
    // 添加缓存
    $cache_key = "user_assets_{$user_id}_{$type}";
    $assets = S($cache_key);
    
    if (!$assets) {
        $assets = M('user_asset')->where(array('user_id'=>$user_id))->select();
        
        foreach($assets as &$asset) {
            $asset['total'] = $asset['balance'] + $asset['frozen'];
            $asset['btc_value'] = $this->convertToBTC($asset['coin'], $asset['total']);
            $asset['usd_value'] = $this->convertToUSD($asset['coin'], $asset['total']);
        }
        
        // 缓存5分钟
        S($cache_key, $assets, 300);
    }
    
    return $assets;
}