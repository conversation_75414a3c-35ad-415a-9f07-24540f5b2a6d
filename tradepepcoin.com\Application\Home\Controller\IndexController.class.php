<?php
namespace Home\Controller;
use Think\Controller;

class IndexController extends Controller {
    
    public function index(){
        // 获取系统信息
        $info = array(
            'site_name' => C('SITE_NAME'),
            'php_version' => PHP_VERSION,
            'thinkphp_version' => THINK_VERSION,
            'server_time' => date('Y-m-d H:i:s'),
        );
        
        $this->assign('info', $info);
        $this->display();
    }
    
    // 行情页面
    public function market(){
        $this->display();
    }
    
    // API测试
    public function test(){
        $data = array(
            'status' => 1,
            'msg' => '系统运行正常',
            'data' => array(
                'time' => time(),
                'date' => date('Y-m-d H:i:s'),
                'server' => $_SERVER['SERVER_NAME'],
            )
        );
        
        $this->ajaxReturn($data);
    }
}