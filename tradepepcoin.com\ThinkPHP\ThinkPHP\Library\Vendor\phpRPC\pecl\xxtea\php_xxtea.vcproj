<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="php_xxtea"
	ProjectGUID="{71165FA5-1EBC-4021-AA17-0CCBC7CD5204}"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug_php5|Win32"
			OutputDirectory=".\Debug_php5"
			IntermediateDirectory=".\Debug_php5"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug_php5/php_xxtea.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="../..,../../main,../../Zend,../../TSRM"
				PreprocessorDefinitions="HAVE_XXTEA;COMPILE_DL_XXTEA;ZTS;NDEBUG;ZEND_WIN32;PHP_WIN32;WIN32;ZEND_DEBUG=1"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				PrecompiledHeaderFile=".\Debug_php5/php_xxtea.pch"
				AssemblerListingLocation=".\Debug_php5/"
				ObjectFile=".\Debug_php5/"
				ProgramDataBaseFileName=".\Debug_php5/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib php5ts.lib"
				OutputFile="Debug_php5\php_xxtea.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="../../Release_TS"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="Debug_php5\php_xxtea.pdb"
				SubSystem="2"
				ImportLibrary="$(OutDir)/php_xxtea.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Debug_php5/php_xxtea.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_php4|Win32"
			OutputDirectory=".\Release_php4"
			IntermediateDirectory=".\Release_php4"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Release_php4/php_xxtea.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/GT /GA "
				Optimization="4"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="2"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="../..,../../main,../../Zend,../../TSRM"
				PreprocessorDefinitions="HAVE_XXTEA;COMPILE_DL_XXTEA;ZTS;NDEBUG;ZEND_WIN32;PHP_WIN32;WIN32;ZEND_DEBUG=0"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Release_php4/php_xxtea.pch"
				AssemblerListingLocation=".\Release_php4/"
				ObjectFile=".\Release_php4/"
				ProgramDataBaseFileName=".\Release_php4/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				CompileAs="1"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib php4ts.lib"
				OutputFile="Release_php4\php_xxtea.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="../../Release_TS"
				ProgramDatabaseFile=".\Release_php4/php_xxtea.pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="$(OutDir)/php_xxtea.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release_php4/php_xxtea.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release_php5|Win32"
			OutputDirectory=".\Release_php5"
			IntermediateDirectory=".\Release_php5"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Release_php5/php_xxtea.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/GT /GA "
				Optimization="4"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="2"
				OmitFramePointers="true"
				AdditionalIncludeDirectories="../..,../../main,../../Zend,../../TSRM"
				PreprocessorDefinitions="HAVE_XXTEA;COMPILE_DL_XXTEA;ZTS;NDEBUG;ZEND_WIN32;PHP_WIN32;WIN32;ZEND_DEBUG=0"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Release_php5/php_xxtea.pch"
				AssemblerListingLocation=".\Release_php5/"
				ObjectFile=".\Release_php5/"
				ProgramDataBaseFileName=".\Release_php5/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				CompileAs="1"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib php5ts.lib"
				OutputFile="Release_php5\php_xxtea.dll"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="../../Release_TS"
				ProgramDatabaseFile=".\Release_php5/php_xxtea.pdb"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				ImportLibrary="$(OutDir)/php_xxtea.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release_php5/php_xxtea.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug_php4|Win32"
			OutputDirectory=".\Debug_php4"
			IntermediateDirectory=".\Debug_php4"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug_php4/php_xxtea.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="../..,../../main,../../Zend,../../TSRM"
				PreprocessorDefinitions="HAVE_XXTEA;COMPILE_DL_XXTEA;ZTS;NDEBUG;ZEND_WIN32;PHP_WIN32;WIN32;ZEND_DEBUG=1"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				PrecompiledHeaderFile=".\Debug_php4/php_xxtea.pch"
				AssemblerListingLocation=".\Debug_php4/"
				ObjectFile=".\Debug_php4/"
				ProgramDataBaseFileName=".\Debug_php4/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib php4ts.lib"
				OutputFile="Debug_php4\php_xxtea.dll"
				LinkIncremental="2"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories="../../Release_TS"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="Debug_php4\php_xxtea.pdb"
				SubSystem="2"
				ImportLibrary="$(OutDir)/php_xxtea.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Debug_php4/php_xxtea.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCWebDeploymentTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm"
			>
			<File
				RelativePath="php_xxtea.c"
				>
				<FileConfiguration
					Name="Debug_php5|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_php4|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release_php5|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug_php4|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<Filter
				Name="lib_xxtea"
				>
				<File
					RelativePath="xxtea.c"
					>
					<FileConfiguration
						Name="Debug_php5|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release_php4|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release_php5|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Debug_php4|Win32"
						>
						<Tool
							Name="VCCLCompilerTool"
							AdditionalIncludeDirectories=""
							PreprocessorDefinitions=""
						/>
					</FileConfiguration>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc"
			>
			<File
				RelativePath="php_xxtea.h"
				>
			</File>
			<Filter
				Name="lib_xxtea"
				>
				<File
					RelativePath="xxtea.h"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe"
			>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
