<?php
/**
 * 加密密码 - 使用更安全的算法
 */
function encrypt_password($password, $salt = '') {
    if (empty($salt)) {
        $salt = generate_salt();
    }
    return password_hash($password . $salt, PASSWORD_ARGON2ID);
}

/**
 * 验证密码
 */
function verify_password($password, $hash, $salt = '') {
    return password_verify($password . $salt, $hash);
}

/**
 * 生成随机盐值
 */
function generate_salt($length = 16) {
    return bin2hex(random_bytes($length));
}
?>
